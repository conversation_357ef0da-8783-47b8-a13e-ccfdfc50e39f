
----------------------------------------------------------------------------------
                        SINGLE CHOICE (<PERSON><PERSON><PERSON><PERSON>)

Thuật toán:

INPUT: answer_id (Int)
ALGORITHM:
1. Tìm answer có answers_id = answer_id
2. Kiểm tra is_correct của answer đó
3. RESULT: is_correct == 1 ? TRUE : FALSE


PHP Implementation:

function evaluateSingleChoice($question, $userAnswerText) {
    $selectedAnswerId = intval($userAnswerText);
    foreach ($question['answers'] as $answer) {
        if ($answer['answers_id'] == $selectedAnswerId) {
            return $answer['is_correct'] == 1;
        }
    }
    return false;
}

PHP Giao Diện:

<div class="question-container">
    <h2><?php echo $question['question_text']; ?></h2>
    <form method="POST" action="submit_answer.php">
        <?php foreach ($question['answers'] as $answer): ?>
            <div class="answer-option">
                <input type="radio" name="answer_id" value="<?php echo $answer['answers_id']; ?>" id="answer_<?php echo $answer['answers_id']; ?>">
                <label for="answer_<?php echo $answer['answers_id']; ?>"><?php echo $answer['answer_text']; ?></label>
            </div>
        <?php endforeach; ?>
        <button type="submit">Nộp bài</button>
    </form>
</div>


----------------------------------------------------------------------------------
                        MATCHING (Nối từ)

Thuật toán:
INPUT: "left_id:right_id,left_id:right_id,..."
ALGORITHM:
1. Parse input thành các cặp (left_id, right_id)
2. Với mỗi cặp:
   - Tìm left_answer có answers_id = left_id
   - Tìm right_answer có answers_id = right_id
   - Kiểm tra: left_answer.match_key == right_answer.answer_text
3. Đếm số cặp đúng
4. RESULT: correct_pairs / total_correct_pairs >= 1.0

PHP Implementation:

function evaluateMatching($question, $userAnswerText) {
    $userMatches = parseMatchingAnswer($userAnswerText);
    $correctAnswers = array_filter($question['answers'], function($a) {
        return $a['is_correct'] == 1;
    });
    $correctPairs = array_column($correctAnswers, 'answer_text', 'match_key');
    
    $correctCount = 0;
    foreach ($userMatches as $match) {
        list($leftId, $rightId) = explode(':', $match);
        $leftAnswer = array_filter($question['answers'], function($a) use ($leftId) {
            return $a['answers_id'] == intval($leftId);
        });
        $rightAnswer = array_filter($question['answers'], function($a) use ($rightId) {
            return $a['answers_id'] == intval($rightId);
        });
        
        if (!empty($leftAnswer) && !empty($rightAnswer)) {
            $leftAnswer = reset($leftAnswer);
            $rightAnswer = reset($rightAnswer);
            if ($correctPairs[$leftAnswer['match_key']] == $rightAnswer['answer_text']) {
                $correctCount++;
            }
        }
    }
    
    return count($correctAnswers) > 0 ? ($correctCount / count($correctAnswers)) : 0.0;
}

PHP Giao Diện:


<div class="matching-container">
    <h2><?php echo $question['question_text']; ?></h2>
    <form method="POST" action="submit_answer.php">
        <div class="left-column">
            <?php foreach ($question['answers'] as $answer): ?>
                <div class="left-option">
                    <span><?php echo $answer['answer_text']; ?></span>
                    <select name="match_<?php echo $answer['answers_id']; ?>">
                        <option value="">Chọn...</option>
                        <?php foreach ($question['answers'] as $rightAnswer): ?>
                            <option value="<?php echo $rightAnswer['answers_id']; ?>"><?php echo $rightAnswer['answer_text']; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endforeach; ?>
        </div>
        <button type="submit">Nộp bài</button>
    </form>
</div>



----------------------------------------------------------------------------------
                        CLASSIFICATION (Phân loại)

Thuật toán:

INPUT: "word_id:category,word_id:category,..."
ALGORITHM:
1. Parse input thành map (word_id -> category)
2. Với mỗi word trong question.answers:
   - Lấy user_category = userMap[word_id]
   - Lấy correct_category = word.match_key (nếu is_correct == 1)
   - So sánh: user_category == correct_category
3. Đếm số từ phân loại đúng
4. RESULT: correct_words / total_words >= 1.0

PHP Implementation:

function evaluateClassification($question, $userAnswerText) {
    $userClassifications = parseClassificationAnswer($userAnswerText);
    $correctCount = 0;
    $totalWords = 0;

    foreach ($question['answers'] as $word) {
        if ($word['is_correct'] == 1) {
            $totalWords++;
            $userCategory = $userClassifications[$word['answers_id']] ?? null;
            $correctCategory = $word['match_key'];

            if ($userCategory == $correctCategory) {
                $correctCount++;
            }
        }
    }

    return $totalWords > 0 ? ($correctCount / $totalWords) : 0.0;
}

PHP Giao Diện:

<div class="classification-container">
    <h2><?php echo $question['question_text']; ?></h2>
    <form method="POST" action="submit_answer.php">
        <div class="classification-options">
            <?php foreach ($question['answers'] as $answer): ?>
                <div class="classification-option">
                    <span><?php echo $answer['answer_text']; ?></span>
                    <select name="classification[<?php echo $answer['answers_id']; ?>]">
                        <option value="">Chọn danh mục...</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category; ?>"><?php echo $category; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endforeach; ?>
        </div>
        <button type="submit">Nộp bài</button>
    </form>
</div>

----------------------------------------------------------------------------------
                        FILL BLANK (Điền từ)

Thuật toán:

INPUT: text (String)
ALGORITHM:
1. Normalize user input (lowercase, trim, remove special chars)
2. Lấy tất cả correct answers (is_correct == 1)
3. Normalize từng correct answer
4. So sánh: normalized_user_input == any(normalized_correct_answers)
5. RESULT: TRUE nếu khớp bất kỳ đáp án đúng nào

PHP Implementation:

function evaluateFillBlank($question, $userAnswerText) {
    $normalizedUser Answer = normalizeFillBlankAnswer($userAnswerText);

    foreach ($question['answers'] as $answer) {
        if ($answer['is_correct'] == 1) {
            $normalizedCorrectAnswer = normalizeFillBlankAnswer($answer['answer_text']);
            if ($normalizedUser Answer == $normalizedCorrectAnswer) {
                return true;
            }
        }
    }
    return false;
}

function normalizeFillBlankAnswer($answer) {
    $normalized = trim(strtolower($answer));
    $normalized = preg_replace('/\s+/', ' ', $normalized);
    $normalized = preg_replace('/[^a-zA-Z0-9\s]/', '', $normalized);
    return $normalized;
}


PHP Giao Diện:

<div class="fill-blank-container">
    <h2><?php echo $question['question_text']; ?></h2>
    <form method="POST" action="submit_answer.php">
        <input type="text" name="user_answer" placeholder="Nhập câu trả lời...">
        <button type="submit">Nộp bài</button>
    </form>
</div>


----------------------------------------------------------------------------------
                        ARRANGEMENT (Sắp xếp từ thành câu)

Thuật toán:

INPUT: "answer_id,answer_id,answer_id,..." (theo thứ tự)
ALGORITHM:
1. Parse input thành array [answer_id1, answer_id2, ...]
2. Lấy correct order từ answers có is_correct == 1, sắp xếp theo order_index
3. So sánh: user_order_array == correct_order_array
4. RESULT: TRUE nếu thứ tự chính xác 100%

PHP Implementation:

function evaluateArrangement($question, $userAnswerText) {
    $userOrderIds = explode(',', $userAnswerText);
    $correctOrder = array_column(array_filter($question['answers'], function($a) {
        return $a['is_correct'] == 1;
    }), 'answers_id');
    
    sort($correctOrder);
    sort($userOrderIds);

    return $userOrderIds == $correctOrder;
}


PHP Giao Diện:

<div class="arrangement-container">
    <h2><?php echo $question['question_text']; ?></h2>
    <form method="POST" action="submit_answer.php">
        <div class="arrangement-options">
            <?php foreach ($question['answers'] as $answer): ?>
                <div class="arrangement-option">
                    <span><?php echo $answer['answer_text']; ?></span>
                    <input type="hidden" name="arrangement[]" value="<?php echo $answer['answers_id']; ?>">
                </div>
            <?php endforeach; ?>
        </div>
        <button type="submit">Nộp bài</button>
    </form>
</div>

----------------------------------------------------------------------------------
                    IMAGE WORD (Nhìn ảnh sắp xếp từ)

Thuật toán:

INPUT: text (String) - từ được sắp xếp
ALGORITHM:
1. Normalize user input (lowercase, chỉ giữ chữ cái)
2. Lấy correct letters từ answers có is_correct == 1, sắp xếp theo order_index
3. Ghép correct letters thành từ đúng
4. Normalize từ đúng
5. So sánh: normalized_user_word == normalized_correct_word
6. RESULT: TRUE nếu từ chính xác


PHP Giao Diện:

<div class="image-word-container">
    <h2><?php echo $question['question_text']; ?></h2>
    <img src="<?php echo $question['media_url']; ?>" alt="Question Image">
    <form method="POST" action="submit_answer.php">
        <input type="text" name="user_word" placeholder="Nhập từ...">
        <button type="submit">Nộp bài</button>
    </form>
</div>