<?php

namespace App\Enum;

enum enrollment: int
{
    // đã hoàn thành: 1 , đang học: 2, đ<PERSON><PERSON>:3, không đạt:4
    case completed = 1;
    case studying = 2;
    case pass = 3;
    case fail = 4;

    public function getEnrollment(): string
    {
        return match ($this) {
            self::completed => 'Chờ xác nhận',
            self::studying => 'Đang học',
            self::pass => 'Đạt',
            self::fail => 'Không đạt',
        };
    }

    public function getStatus(): string
    {
        return match ($this) {
            self::studying => 'card-status-studying',
            self::pass => 'card-status-pass',
            self::fail => 'card-status-fail',
        };
    }
}
