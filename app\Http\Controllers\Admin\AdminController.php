<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Models\Student;
use Illuminate\Http\Request;
use App\Http\Requests\AddStudentRequest;
use App\Enum\personStatus;
use App\Http\Requests\AddCourseRequest;
use App\Models\Teacher;
use App\Http\Requests\AddTeacherRequest;
use App\Models\Course;
use App\Http\Requests\EditCourseResquest;
use App\Models\TeacherCourseAssignment;
use App\Enum\courseStatus;
use App\Models\Lesson;
use App\Http\Requests\AddLevelRequest;
use App\Models\LessonPart;
use App\Http\Requests\UpdateLessonRequest;
use App\Http\Requests\UpdateLessonPartRequest;
use App\Models\Question;
use App\Models\Answer;
use App\Models\Notification;
use App\Http\Requests\NotificationRequest;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    // hiển thị thông tin của admin
    public function AdminHome(Request $request)
    {
        $admin = Auth::user();
        $notifications = Notification::orderBy('created_at', 'desc')->paginate(12);
        $CoutNotifications = Notification::count();
        // Nếu trang yêu cầu lớn hơn trang tối đa, chuyển hướng về trang cuối
        if ($request->page > $notifications->lastPage()) {
            return redirect()->route('admin.home', ['page' => $notifications->lastPage()]);
        }

        return view('admin.home')->with('admin', $admin)->with('notifications', $notifications)->with('CoutNotifications', $CoutNotifications);
    }
    //THêm thông báo
    public function AddNotification(NotificationRequest $request)
    {
        $admin_id = Auth::user()->admin_id;
        Notification::create([
            'admin_id' => $admin_id,
            'title' => $request->title,
            'message' => $request->message,
            'notification_date' => now(),
        ]);
        
        return redirect()->back()->with('success', 'Thông báo đã được thêm thành công!');
    }
    //Xóa thông báo
    public function DeleteNotification($id)
    {
        $notification = Notification::findOrFail($id);
        $notification->delete();
        return redirect()->back()->with('success', 'Thông báo đã được xóa thành công!');
    }
    //Lấy ra danh sách sinh viên có kết hợp tìm kiếm
    public function GetStudentList(Request $request)
    {
        $query = Student::query();

        // Tìm kiếm theo tên
        if ($request->filled('search')) {
            $query->where('fullname', 'like', '%' . $request->search . '%');
        }

        // Lọc theo trạng thái (1: Hoạt động, 0: Không hoạt động)
        if ($request->filled('status')) {
            $query->where('is_status', $request->status);
        }

        // Lọc theo giới tính (1: Nam, 0: Nữ)
        if ($request->filled('gender')) {
            $query->where('gender', $request->gender);
        }

        $students = $query->paginate(2)->appends($request->all());

        // Nếu trang hiện tại lớn hơn trang cuối, chuyển về trang cuối
        if ($students->currentPage() > $students->lastPage()) {
            return redirect()->route('admin.studentlist', ['page' => $students->lastPage()]);
        }


        $total = Student::count();
        $active = Student::where('is_status', 1)->count();
        $inactive = Student::where('is_status', 0)->count();

        return view('admin.StudentList')
            ->with('total', $total)
            ->with('active', $active)
            ->with('inactive', $inactive)
            ->with('students', $students);
    }

    //Thêm sinh viên
    public function AddStudents(AddStudentRequest $request)
    {
        $data = $request->validated();

        $DOB = $data['date_of_birth'];
        $cleanDob = date('dmY', strtotime($DOB));

        $data['username'] = $data['email'];
        $data['password'] = bcrypt($cleanDob);

        if ($request->hasFile('avatar')) {
            $file = $request->file('avatar');
            $filename = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('uploads/avatars'), $filename);
            $data['avatar'] = $filename;
        } else {
            $data['avatar'] = 'AvtMacDinh.jpg';
        }
        Student::create($data);
        return redirect()->back()->with('success', 'Thêm sinh viên thành công!');
    }

    //Thay đổi trạng thái sinh viên
    public function AjaxToggleStatus(int $id)
    {
        $student = Student::find($id);
        if (!$student) {
            return response()->json(['success' => false], 404);
        }

        $student->is_status = $student->is_status === personStatus::ACTIVE
            ? personStatus::INACTIVE
            : personStatus::ACTIVE;
        $student->save();
        return response()->json([
            'success' => true,
            'new_status_text' => $student->is_status->getStatus(),
            'badge_class' => $student->is_status->badgeClass(),
        ]);
    }

    //Danh sách quản lý giáo viên có kết hợp tìm kiếm
    public function GetTeacherList(Request $request)
    {

        $query = Teacher::query();

        // Tìm kiếm theo tên
        if ($request->filled('search')) {
            $query->where('fullname', 'like', '%' . $request->search . '%');
        }

        // Lọc theo trạng thái (1: Hoạt động, 0: Không hoạt động)
        if ($request->filled('status')) {
            $query->where('is_status', $request->status);
        }

        // Lọc theo giới tính (1: Nam, 0: Nữ)
        if ($request->filled('gender')) {
            $query->where('gender', $request->gender);
        }

        $Teachers = $query->paginate(2)->appends($request->all());
        // Nếu trang hiện tại lớn hơn trang cuối, chuyển về trang cuối
        if ($Teachers->currentPage() > $Teachers->lastPage()) {
            return redirect()->route('admin.teacherlist', ['page' => $Teachers->lastPage()]);
        }
        $total = Teacher::count();
        $active = Teacher::where('is_status', 1)->count();
        $inactive = Teacher::where('is_status', 0)->count();

        return view('admin.TeacherList')
            ->with('total', $total)
            ->with('active', $active)
            ->with('inactive', $inactive)
            ->with('teachers', $Teachers);
    }

    //Thêm giáo viên
    public function AddTeachers(AddTeacherRequest $request)
    {
        $data = $request->validated();

        $DOB = $data['date_of_birth'];
        $cleanDob = date('dmY', strtotime($DOB));

        $data['username'] = $data['email'];
        $data['password'] = bcrypt($cleanDob);

        if ($request->hasFile('avatar')) {
            $file = $request->file('avatar');
            $filename = time() . '_' . $file->getClientOriginalName();
            $file->move(public_path('uploads/avatars'), $filename);
            $data['avatar'] = $filename;
        } else {
            $data['avatar'] = 'AvtMacDinh.jpg';
        }

        Teacher::create($data);

        return redirect()->back()->with('success', 'Thêm giáo viên thành công!');
    }

    // Thay đổi trạng thái giáo viên 
    public function AjaxToggleStatusTeacher(int $id)
    {
        $teacher = Teacher::find($id);
        if (!$teacher) {
            return response()->json(['success' => false], 404);
        }

        $teacher->is_status = $teacher->is_status === personStatus::ACTIVE
            ? personStatus::INACTIVE
            : personStatus::ACTIVE;
        $teacher->save();
        return response()->json([
            'success' => true,
            'new_status_text' => $teacher->is_status->getStatus(),
            'badge_class' => $teacher->is_status->badgeClass(),
        ]);
    }

    //Danh sách các khóa học có kết hợp tìm kiếm
    public function GetCourseList(Request $request)
    {
        $query = Course::with(['teachers', 'lesson']);

        // Lọc theo tên khóa học
        if ($request->filled('search')) {
            $query->where('course_name', 'like', '%' . $request->search . '%');
        }

        // Lọc theo trình độ
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        // Lọc theo trạng thái
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Lọc theo năm
        if ($request->filled('year')) {
            $query->whereYear('created_at', $request->year);
        }

        $courses = $query->orderByRaw("FIELD(status, 'Chờ xác thực', 'Đang mở lớp', 'Đã hoàn thành')")->paginate(10)->appends($request->all());

        if ($courses->currentPage() > $courses->lastPage()) {
            return redirect()->route('admin.courses', ['page' => $courses->lastPage()]);
        }

        $levels = Course::select('level')->distinct()->pluck('level');
        $statuses = Course::select('status')->distinct()->pluck('status');
        $years = Course::selectRaw('YEAR(starts_date) as year')->distinct()->pluck('year');

        return view('admin.CourseManagement', compact('courses', 'levels', 'statuses', 'years'));
    }


    // Tạo khóa học mới
    public function CreateCourse(AddCourseRequest $request)
    {
        Course::create([
            'course_name' => $request->course_name,
            'level' => $request->level,
            'year' => $request->year,
            'description' => $request->description,
            'starts_date' => $request->starts_date,
            'status' => 'Chờ xác thực',
        ]);
        return redirect()->back()->with('success', 'Khóa học đã được tạo thành công!');
    }
    // cập nhật thông tin khóa học
    public function CourseUpdate(EditCourseResquest $request, $id)
    {
        try {
            DB::transaction(function () use ($request, $id) {
                $course = Course::findOrFail($id);
                $course->update($request->validated());

                $course->enrollments()
                    ->where('status', '!=', 0)
                    ->update(['status' => 0]);
            });

            return redirect()
                ->back()
                ->with('success', 'Cập nhật khóa học thành công và đã đánh dấu hoàn thành cho các học viên.');
        } catch (QueryException $e) {
            // ghi log chi tiết lỗi để sau này trace
            Log::error('CourseUpdate failed', [
                'course_id' => $id,
                'error'     => $e->getMessage(),
            ]);

            // đưa flash message rõ ràng cho người dùng
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Đã có lỗi trong quá trình cập nhật, vui lòng thử lại hoặc liên hệ quản trị.');
        }
    }
    // xóa khóa học 
    public function CourseDelete($id)
    {
        $course = Course::findOrFail($id);

        $course->delete();

        return redirect()->back()->with('success', 'Xóa khóa học thành công!');
    }
    // Phân công giảng dạy
    public function showUnassignedCourses()
    {
        // Lấy các khóa học có trạng thái là "Chờ xác thực" hoặc "Đang mở lớp"
        $courses = Course::whereIn('status', [
            courseStatus::verifying->value,
            courseStatus::IsOpening->value,
        ])
            ->orderByRaw("
        CASE 
            WHEN status = ? THEN 1 
            WHEN status = ? THEN 2 
            ELSE 3 
        END
    ", [
                courseStatus::verifying->value,
                courseStatus::IsOpening->value,
            ])
            ->get();

        // Lấy tất cả giáo viên đang hoạt động
        $teachers = Teacher::where('is_status', 1)->get();

        // Lấy danh sách giáo viên đã được phân công cho các khóa học này
        $assignments = TeacherCourseAssignment::with('teacher')
            ->whereIn('course_id', $courses->pluck('course_id'))
            ->get();

        // Gom theo course_id để hiển thị trong view
        $courseAssignments = [];
        foreach ($assignments as $assignment) {
            $courseAssignments[$assignment->course_id][] = [
                'teacher' => $assignment->teacher,
                'position' => $assignment->role, // 'giảng viên chính' hoặc 'trợ giảng'
            ];
        }
        return view('admin.TeachingAssignments', [
            'unassignedCourses' => $courses,
            'teachers' => $teachers,
            'courseAssignments' => $courseAssignments,
        ]);
    }
    // Phân công giáo viên
    public function assignTeacher(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,course_id',
            'teacher_id' => 'required|exists:teachers,teacher_id',
            'role' => 'required|in:Main Teacher,Assistant Teacher',
        ]);

        // Kiểm tra nếu giáo viên đã được phân công vào khóa học này
        $exists = TeacherCourseAssignment::where('course_id', $request->course_id)
            ->where('teacher_id', $request->teacher_id)
            ->exists();

        if ($exists) {
            return back()->with('error', 'Giáo viên này đã được phân công cho khóa học.');
        }

        TeacherCourseAssignment::create([
            'course_id' => $request->course_id,
            'teacher_id' => $request->teacher_id,
            'role' => $request->role,
            'assigned_at' => now(),
        ]);

        return back()->with('success', 'Phân công giáo viên thành công.');
    }

    //Xóa phân công 
    public function removeTeacher(Request $request)
    {
        $courseId = $request->input('course_id');
        $teacherId = $request->input('teacher_id');
        TeacherCourseAssignment::where('course_id', $courseId)
            ->where('teacher_id', $teacherId)
            ->delete();
        return back()->with('success', 'Đã xoá giáo viên khỏi khóa học.');
    }
    //Danh sách các trình độ
    public function ShowListLesson()
    {
        $lessons = Lesson::with('lessonParts')->orderBy('order_index')->get();
        return view('admin.showlesson', compact('lessons'));
    }
    // thêm trình độ mới 
    public function store(AddLevelRequest $request)
    {
        Lesson::create($request->validated());

        return redirect()->back()->with('success', 'Thêm trình độ thành công!');
    }
    //danh sách tên trình độ
    public function showLessonsWithLevels()
    {
        // Lấy danh sách các level duy nhất từ lessons
        $levels = Lesson::select('level')->distinct()->orderBy('level')->pluck('level');

        // Truyền xuống view
        return view('admin.AddQuestion', compact('levels'));
    }
    //Danh sách các bài học
    public function getLessonsByLevel($level)
    {
        $lessonPart = LessonPart::where('level', $level)
            ->get();

        return response()->json($lessonPart);
    }
    //Cập nhật lesson
    public function EditLesson(UpdateLessonRequest $request, string $level)
    {
        // Tìm bài học theo level, nếu không tìm thấy sẽ 404
        $lesson = Lesson::where('level', $level)->firstOrFail();

        // Cập nhật
        $lesson->update([
            'level'       => $request->input('level'),
            'title'       => $request->input('title'),
            'description' => $request->input('description'),
            'order_index' => $request->input('order_index'),
        ]);

        return redirect()->back()->with('success', 'cập nhật trình độ thành công!');
    }
    //Cập nhật lesson_part 
    public function EditLessonPart(UpdateLessonPartRequest  $request, $lesson_part_id)
    {
        $part = LessonPart::findOrFail($lesson_part_id);

        $part->update($request->validated());

        return back()->with('success', 'Đã cập nhật phần học.');
    }



    //Thêm câu hỏi
    public function AddQuestion(Request $request)
    {

        $type = $request->input('question_type');

        switch ($type) {
            case 'single_choice':
                return $this->handleSingleChoice($request);
            case 'matching':
                return $this->handleMatching($request);
            case 'classification':
                return $this->handleClassification($request);
            case 'fill_blank':
                return $this->handleFillBlank($request);
            case 'arrangement':
                return $this->handleArrangement($request);
            case 'image_word':
                return $this->handleImageWord($request);
            default:
                return back()->withErrors(['message' => 'Loại câu hỏi không hợp lệ']);
        }
    }

    private function handleSingleChoice(Request $request)
    {
        // Validate input
        $request->validate([
            'lesson_part_id' => 'required|exists:lesson_parts,lesson_part_id',
            'question_text' => 'required|string',
            'order_index' => 'required|integer',
            'answers' => 'required|array|min:2|max:6',
            'answers.*' => 'required|string',
            'correct_answer' => 'required|integer|min:0',
        ]);

        // 1. Tạo câu hỏi
        $question = Question::create([
            'lesson_part_id' => $request->lesson_part_id,
            'question_type' => 'single_choice',
            'question_text' => $request->question_text,
            'media_url' => null,
            'order_index' => $request->order_index,
        ]);

        // 2. Tạo các đáp án
        foreach ($request->answers as $index => $answerText) {
            Answer::create([
                'questions_id' => $question->questions_id,
                'match_key' => null,
                'answer_text' => $answerText,
                'is_correct' => $index == $request->correct_answer ? 1 : 0,
                'feedback' => $index == $request->correct_answer
                    ? ($request->correct_feedback ?? 'Correct!')
                    : ($request->wrong_feedback ?? 'Incorrect!'),
                'media_url' => null,
                'order_index' => $index + 1,
            ]);
        }

        return back()->with('success', 'Tạo câu hỏi trắc nghiệm thành công!');
    }

    private function handleMatching(Request $request)
    {
        // Validate input
        $request->validate([
            'lesson_part_id' => 'required|exists:lesson_parts,lesson_part_id',
            'question_text' => 'required|string',
            'order_index' => 'required|integer',
            'left_items' => 'required|array|min:2',
            'left_items.*' => 'required|string',
            'right_images' => 'sometimes|array',
            'right_images.*' => 'sometimes|image|mimes:jpeg,png,jpg,gif|max:2048',
            'right_texts' => 'sometimes|array',
            'right_texts.*' => 'sometimes|string',
        ]);

        // 1. Tạo câu hỏi
        $question = Question::create([
            'lesson_part_id' => $request->lesson_part_id,
            'question_type' => 'matching',
            'question_text' => $request->question_text,
            'media_url' => null,
            'order_index' => $request->order_index,
        ]);

        // 2. Tạo các đáp án bên trái (words/text)
        foreach ($request->left_items as $index => $leftItem) {
            Answer::create([
                'questions_id' => $question->questions_id,
                'answer_text' => $leftItem,
                'match_key' => strtolower(trim($leftItem)), // match_key để nối với right items
                'is_correct' => 1, // Left items là correct
                'feedback' => null,
                'media_url' => null,
                'order_index' => $index + 1,
            ]);
        }

        // 3. Tạo các đáp án bên phải (images hoặc text)
        $rightItemsCount = max(
            count($request->right_images ?? []),
            count($request->right_texts ?? [])
        );

        for ($index = 0; $index < $rightItemsCount; $index++) {
            $mediaUrl = null;
            $answerText = '';

            // Xử lý upload ảnh cho right items
            if (isset($request->right_images[$index])) {
                $image = $request->right_images[$index];
                $imageName = time() . '_' . $index . '_' . $image->getClientOriginalName();
                $directory = 'uploads/matching';

                // Tạo thư mục nếu chưa có
                if (!file_exists(public_path($directory))) {
                    mkdir(public_path($directory), 0755, true);
                }

                $image->move(public_path($directory), $imageName);
                $mediaUrl = url($directory . '/' . $imageName); // Full URL với https://
                $answerText = $request->left_items[$index] ?? 'Image ' . ($index + 1);
            } elseif (isset($request->right_texts[$index])) {
                $answerText = $request->right_texts[$index];
            }

            if (!empty($answerText)) {
                Answer::create([
                    'questions_id' => $question->questions_id,
                    'answer_text' => $answerText,
                    'match_key' => null, // Right items không có match_key
                    'is_correct' => 0, // Right items không được đánh dấu correct
                    'feedback' => null,
                    'media_url' => $mediaUrl, // URL đầy đủ với https://
                    'order_index' => count($request->left_items) + $index + 1,
                ]);
            }
        }

        return back()->with('success', 'Tạo câu hỏi nối từ thành công!');
    }

    private function handleClassification(Request $request)
    {
        // Validate input
        $request->validate([
            'lesson_part_id' => 'required|exists:lesson_parts,lesson_part_id',
            'question_text' => 'required|string',
            'order_index' => 'required|integer',
            'words' => 'required|array|min:3',
            'word_categories' => 'required|array', // word_index => category mapping
        ]);

        // 1. Tạo câu hỏi
        $question = Question::create([
            'lesson_part_id' => $request->lesson_part_id,
            'question_type' => 'classification',
            'question_text' => $request->question_text,
            'media_url' => null,
            'order_index' => $request->order_index,
        ]);

        // 2. Tạo các từ cần phân loại
        foreach ($request->words as $index => $word) {
            $category = $request->word_categories[$index] ?? null;

            Answer::create([
                'questions_id' => $question->questions_id,
                'answer_text' => trim($word),
                'match_key' => $category, // Category mà từ này thuộc về
                'is_correct' => 1, // Tất cả words đều là correct
                'feedback' => null,
                'media_url' => null,
                'order_index' => $index + 1,
            ]);
        }

        return back()->with('success', 'Tạo câu hỏi phân loại từ thành công!');
    }

    private function handleFillBlank(Request $request)
    {
        // Validate input
        $request->validate([
            'lesson_part_id' => 'required|exists:lesson_parts,lesson_part_id',
            'question_text' => 'required|string',
            'order_index' => 'required|integer',
            'correct_answers' => 'required|array|min:1',
            'correct_answers.*' => 'required|string',
        ]);

        // 1. Tạo câu hỏi
        $question = Question::create([
            'lesson_part_id' => $request->lesson_part_id,
            'question_type' => 'fill_blank',
            'question_text' => $request->question_text, // Ví dụ: I ___ to school every day.
            'media_url' => null,
            'order_index' => $request->order_index,
        ]);

        // 2. Tạo các đáp án đúng (có thể có nhiều đáp án đúng)
        foreach ($request->correct_answers as $index => $correctAnswer) {
            Answer::create([
                'questions_id' => $question->questions_id,
                'answer_text' => trim($correctAnswer), // ví dụ: "go", "walk", "run"
                'match_key' => null,
                'is_correct' => 1,
                'feedback' => $request->correct_feedback ?? 'Correct!',
                'media_url' => null,
                'order_index' => $index + 1,
            ]);
        }

        return back()->with('success', 'Tạo câu hỏi điền chỗ trống thành công!');
    }

    private function handleArrangement(Request $request)
    {
        // Validate input
        $request->validate([
            'lesson_part_id' => 'required|exists:lesson_parts,lesson_part_id',
            'question_text' => 'required|string',
            'order_index' => 'required|integer',
            'sentence_words' => 'required|array|min:2',
            'sentence_words.*' => 'required|string',
        ]);

        // 1. Tạo câu hỏi
        $question = Question::create([
            'lesson_part_id' => $request->lesson_part_id,
            'question_type' => 'arrangement',
            'question_text' => $request->question_text,
            'media_url' => null,
            'order_index' => $request->order_index,
        ]);

        // 2. Tạo các từ theo thứ tự đúng
        foreach ($request->sentence_words as $index => $word) {
            Answer::create([
                'questions_id' => $question->questions_id,
                'answer_text' => trim($word),
                'match_key' => null,
                'is_correct' => 1, // Tất cả words đều correct
                'feedback' => null,
                'media_url' => null,
                'order_index' => $index + 1, // Thứ tự đúng của từ trong câu
            ]);
        }

        return back()->with('success', 'Tạo câu hỏi sắp xếp câu thành công!');
    }

    private function handleImageWord(Request $request)
    {
        // Validate input
        $request->validate([
            'lesson_part_id' => 'required|exists:lesson_parts,lesson_part_id',
            'question_text' => 'required|string',
            'order_index' => 'required|integer',
            'correct_word' => 'required|string|regex:/^[a-zA-Z]+$/',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // 1. Upload image và tạo URL đầy đủ
        $imageUrl = null;
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $directory = 'uploads/image_word';

            // Tạo thư mục nếu chưa có
            if (!file_exists(public_path($directory))) {
                mkdir(public_path($directory), 0755, true);
            }

            $image->move(public_path($directory), $imageName);
            $imageUrl = url($directory . '/' . $imageName); // Full URL với https://
        }

        // 2. Tạo câu hỏi với media_url là URL đầy đủ
        $question = Question::create([
            'lesson_part_id' => $request->lesson_part_id,
            'question_type' => 'image_word',
            'question_text' => $request->question_text,
            'media_url' => $imageUrl, // URL đầy đủ với https://
            'order_index' => $request->order_index,
        ]);

        // 3. Tạo các chữ cái theo thứ tự đúng
        $letters = str_split(strtolower(trim($request->correct_word)));
        foreach ($letters as $index => $letter) {
            Answer::create([
                'questions_id' => $question->questions_id,
                'answer_text' => $letter,
                'match_key' => null,
                'is_correct' => 1, // Tất cả letters đều correct
                'feedback' => null,
                'media_url' => null,
                'order_index' => $index + 1, // Thứ tự đúng của chữ cái
            ]);
        }

        return back()->with('success', 'Tạo câu hỏi nhìn ảnh ghép từ thành công!');
    }

    /**
     * Get question types for form dropdown
     */
    public function getQuestionTypes()
    {
        return [
            'single_choice' => 'Trắc nghiệm (Single Choice)',
            'matching' => 'Nối từ (Matching)',
            'classification' => 'Phân loại từ (Classification)',
            'fill_blank' => 'Điền chỗ trống (Fill Blank)',
            'arrangement' => 'Sắp xếp từ thành câu (Arrangement)',
            'image_word' => 'Nhìn ảnh ghép từ (Image Word)',
        ];
    }

    /**
     * Validate question creation based on type
     */
    private function validateQuestionByType(Request $request, $type)
    {
        $baseRules = [
            'lesson_part_id' => 'required|exists:lesson_parts,lesson_part_id',
            'question_text' => 'required|string|max:1000',
            'order_index' => 'required|integer|min:1',
        ];

        $typeSpecificRules = [];

        switch ($type) {
            case 'single_choice':
                $typeSpecificRules = [
                    'answers' => 'required|array|min:2|max:6',
                    'answers.*' => 'required|string|max:255',
                    'correct_answer' => 'required|integer|min:0',
                ];
                break;

            case 'matching':
                $typeSpecificRules = [
                    'left_items' => 'required|array|min:2|max:10',
                    'right_items' => 'required|array|min:2|max:10',
                    'left_items.*' => 'required|string|max:255',
                    'right_items.*' => 'required|string|max:255',
                ];
                break;

            case 'classification':
                $typeSpecificRules = [
                    'words' => 'required|array|min:3|max:20',
                    'categories' => 'required|array|min:2|max:5',
                    'word_categories' => 'required|array',
                ];
                break;

            case 'fill_blank':
                $typeSpecificRules = [
                    'correct_answers' => 'required|array|min:1|max:5',
                    'correct_answers.*' => 'required|string|max:100',
                ];
                break;

            case 'arrangement':
                $typeSpecificRules = [
                    'sentence_words' => 'required|array|min:2|max:15',
                    'sentence_words.*' => 'required|string|max:50',
                ];
                break;

            case 'image_word':
                $typeSpecificRules = [
                    'correct_word' => 'required|string|max:20|regex:/^[a-zA-Z]+$/',
                    'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
                ];
                break;
        }

        return $request->validate(array_merge($baseRules, $typeSpecificRules));
    }

    /**
     * Get next order index for a lesson part
     */
    private function getNextOrderIndex($lessonPartId)
    {
        $maxOrder = Question::where('lesson_part_id', $lessonPartId)->max('order_index');
        return ($maxOrder ?? 0) + 1;
    }

    /**
     * Upload and save question media file with full URL
     */
    private function uploadQuestionMedia($file, $type = 'general')
    {
        if (!$file) return null;

        $directory = 'uploads/questions/' . $type;
        $filename = time() . '_' . $file->getClientOriginalName();

        // Create directory if it doesn't exist
        if (!file_exists(public_path($directory))) {
            mkdir(public_path($directory), 0755, true);
        }

        $file->move(public_path($directory), $filename);

        // Return full URL with https://
        return url($directory . '/' . $filename);
    }

    /**
     * Upload matching answer image and return full URL
     */
    private function uploadMatchingImage($file, $index)
    {
        if (!$file) return null;

        $directory = 'uploads/matching';
        $filename = time() . '_' . $index . '_' . $file->getClientOriginalName();

        // Create directory if it doesn't exist
        if (!file_exists(public_path($directory))) {
            mkdir(public_path($directory), 0755, true);
        }

        $file->move(public_path($directory), $filename);

        // Return full URL with https://
        return url($directory . '/' . $filename);
    }

    /**
     * Upload image word question image and return full URL
     */
    private function uploadImageWordImage($file)
    {
        if (!$file) return null;

        $directory = 'uploads/image_word';
        $filename = time() . '_' . $file->getClientOriginalName();

        // Create directory if it doesn't exist
        if (!file_exists(public_path($directory))) {
            mkdir(public_path($directory), 0755, true);
        }

        $file->move(public_path($directory), $filename);

        // Return full URL with https://
        return url($directory . '/' . $filename);
    }
}
