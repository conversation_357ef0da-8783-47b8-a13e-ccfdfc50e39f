<?php if($paginator->hasPages()): ?>
    <ul class="pagination justify-content-center">
        
        <?php if($paginator->onFirstPage()): ?>
            <li class="page-item disabled" aria-disabled="true">
                <span class="page-link">&laquo;</span>
            </li>
        <?php else: ?>
            <li class="page-item">
                <a class="page-link" href="<?php echo e($paginator->previousPageUrl()); ?>" rel="prev">&laquo;</a>
            </li>
        <?php endif; ?>

        
        <?php
            $total = $paginator->lastPage();
            $current = $paginator->currentPage();
            $start = max(1, $current - 2);
            $end = min($total, $current + 2);
        ?>

        
        <?php if($start > 1): ?>
            <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url(1)); ?>">1</a></li>
            <?php if($start > 2): ?>
                <li class="page-item disabled"><span class="page-link">...</span></li>
            <?php endif; ?>
        <?php endif; ?>

        
        <?php for($i = $start; $i <= $end; $i++): ?>
            <?php if($i == $current): ?>
                <li class="page-item active" aria-current="page"><span class="page-link"><?php echo e($i); ?></span></li>
            <?php else: ?>
                <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($i)); ?>"><?php echo e($i); ?></a></li>
            <?php endif; ?>
        <?php endfor; ?>

        
        <?php if($end < $total): ?>
            <?php if($end < $total - 1): ?>
                <li class="page-item disabled"><span class="page-link">...</span></li>
            <?php endif; ?>
            <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($total)); ?>"><?php echo e($total); ?></a></li>
        <?php endif; ?>

        
        <?php if($paginator->hasMorePages()): ?>
            <li class="page-item">
                <a class="page-link" href="<?php echo e($paginator->nextPageUrl()); ?>" rel="next">&raquo;</a>
            </li>
        <?php else: ?>
            <li class="page-item disabled" aria-disabled="true">
                <span class="page-link">&raquo;</span>
            </li>
        <?php endif; ?>
    </ul>
<?php endif; ?>
<?php /**PATH D:\Temp\nhut\resources\views/vendor/pagination/bootstrap-5.blade.php ENDPATH**/ ?>