/* Exercise CSS Styles */
    * {
            box-sizing: border-box;
        }

        body {
            color: #333;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem;
            margin: 0;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 2rem;
            text-align: center;
            color: white;
        }

        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .progress-container {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            height: 8px;
            margin-top: 1rem;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ffd93d);
            border-radius: 50px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .question-block {
            display: none;
            padding: 2.5rem;
            animation: fadeIn 0.5s ease-in-out;
        }

        .question-block.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .question-counter {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .question-text {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            line-height: 1.6;
            color: #2c3e50;
            font-weight: 500;
        }

        .answers {
            list-style: none;
            padding: 0;
            margin-bottom: 2rem;
        }

        .answers li {
            margin-bottom: 1rem;
        }

        .answers input[type="radio"] {
            display: none;
        }

        .answer-btn {
            display: block;
            width: 100%;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #dee2e6;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-size: 1rem;
            line-height: 1.5;
            color: #495057;
            position: relative;
            overflow: hidden;
        }

        .answer-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .answer-btn:hover {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-color: #2196f3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }

        .answer-btn:hover::before {
            left: 100%;
        }

        .answers input[type="radio"]:checked+.answer-btn {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            border-color: #4caf50;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .answers input[type="radio"]:disabled+.answer-btn {
            cursor: not-allowed;
            opacity: 0.7;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #fff;
            border: none;
            padding: 0.8rem 2rem;
            cursor: pointer;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
            white-space: nowrap;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: linear-gradient(135deg, #bbb, #999);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.6;
        }

        .btn-submit {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .btn-submit:hover:not(:disabled) {
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .btn-complete {
            background: linear-gradient(135deg, #4caf50, #45a049);
        }

        .btn-complete:hover {
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .btn-review {
            background: linear-gradient(135deg, #ffc107, #ff9800);
            color: #fff;
        }

        .btn-review:hover {
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
        }

        /* FIXED: Button Group Layout */
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
            align-items: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }

        /* Layout khi có 2 nút */
        .btn-group:has(.btn-prev):has(.btn-next) {
            justify-content: space-between;
        }

        /* Layout khi có 3 nút */
        .btn-group:has(.btn-prev):has(.btn-submit):has(.btn-complete) {
            justify-content: space-between;
        }

        /* Layout khi chỉ có 1 nút */
        .btn-group:has(.btn:only-child) {
            justify-content: center;
        }

        .feedback {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 10px;
            font-style: italic;
            border: 1px solid #ddd;
            background: #f8f9fa;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .feedback.correct {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #2e7d32;
            border-color: #4caf50;
        }

        .feedback.incorrect {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border-color: #f44336;
        }

        .answer-btn.correct {
            background: linear-gradient(135deg, #4caf50, #45a049) !important;
            color: white !important;
            border-color: #4caf50 !important;
        }

        .answer-btn.incorrect {
            background: linear-gradient(135deg, #f44336, #e53935) !important;
            color: white !important;
            border-color: #f44336 !important;
        }

        .loading {
            display: none;
            padding: 2.5rem;
            text-align: center;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Responsive Design - IMPROVED */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .question-block {
                padding: 1.5rem;
            }

            .btn {
                padding: 0.7rem 1.5rem;
                font-size: 0.9rem;
                min-width: 100px;
            }

            .btn-group {
                flex-direction: column;
                gap: 8px;
            }

            .btn-group .btn {
                width: 100%;
                max-width: 280px;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .btn-group {
                justify-content: space-around;
            }
        }

        .fill-blank-input {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            transition: border-color 0.3s ease;
        }

        .fill-blank-input.correct {
            border-color: #4caf50;
            background-color: #e8f5e8;
        }

        .fill-blank-input.incorrect {
            border-color: #f44336;
            background-color: #ffebee;
        }

        .correct-answer {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 0.75rem;
            border-radius: 8px;
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }

        /* Thông báo validation */
        .validation-message {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 1px solid #ffc107;
            color: #856404;
            padding: 0.75rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-size: 0.9rem;
            display: none;
            animation: shake 0.5s ease-in-out;
        }

        .validation-message.show {
            display: block;
        }

        @keyframes shake {

            0%,
            100% {
                transform: translateX(0);
            }

            25% {
                transform: translateX(-5px);
            }

            75% {
                transform: translateX(5px);
            }
        }

        /* Hiển thị đáp án đúng */
        .correct-answer {
            margin-top: 1rem;
            padding: 1rem;
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border: 1px solid #4caf50;
            border-radius: 8px;
            color: #2e7d32;
            font-weight: 500;
        }

        .correct-answer strong {
            color: #1b5e20;
        }

        /* CSS cho thống kê bài làm - Thêm vào phần style hiện tại */

        .statistics-view {
            text-align: center;
            padding: 2.5rem;
        }

        .statistics-header {
            margin-bottom: 2rem;
        }

        .statistics-header h2 {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .score-summary {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }

        .score-number {
            font-size: 1.8rem;
            font-weight: 700;
            line-height: 1;
        }

        .score-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .score-percentage {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .percentage-number {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .percentage-label {
            font-size: 1.1rem;
            color: #666;
            margin-top: 0.5rem;
        }

        .statistics-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .stat-item {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
        }

        .correct-stat {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border: 2px solid #4caf50;
        }

        .incorrect-stat {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            border: 2px solid #f44336;
        }

        .total-stat {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #2196f3;
        }

        .stat-icon {
            font-size: 2rem;
            margin-right: 1rem;
        }

        .stat-content {
            flex: 1;
            text-align: left;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.95rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .performance-message {
            margin: 2rem 0;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .performance {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: left;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .performance-icon {
            font-size: 3rem;
            margin-right: 1.5rem;
        }

        .performance-text h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.3rem;
            color: #2c3e50;
        }

        .performance-text p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }

        .performance.excellent {
            background: linear-gradient(135deg, #fff9c4, #fff59d);
            border: 2px solid #ffeb3b;
        }

        .performance.good {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border: 2px solid #4caf50;
        }

        .performance.average {
            background: linear-gradient(135deg, #fff3e0, #ffcc02);
            border: 2px solid #ff9800;
        }

        .performance.needs-work {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            border: 2px solid #f44336;
        }

        .btn-review {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            margin-right: 1rem;
        }

        .btn-review:hover {
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }

        /* Responsive cho thống kê */
        @media (max-width: 768px) {
            .score-summary {
                flex-direction: column;
                gap: 1.5rem;
            }

            .score-circle {
                width: 100px;
                height: 100px;
            }

            .score-number {
                font-size: 1.5rem;
            }

            .percentage-number {
                font-size: 2.5rem;
            }

            .statistics-details {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-item {
                padding: 1rem;
            }

            .performance {
                flex-direction: column;
                text-align: center;
            }

            .performance-icon {
                margin-right: 0;
                margin-bottom: 1rem;
            }

            .performance-text {
                text-align: center;
            }
        }
        /* Matching Game Styles */
.matching-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin: 20px 0;
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #e9ecef;
}

.matching-row {
    display: flex;
    align-items: center;
    gap: 20px;
    min-height: 120px;
}

/* Text Items Column */
.text-items-column {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: flex-start;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 2px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.matching-item {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    text-transform: capitalize;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    position: relative;
}

.matching-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.matching-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg) scale(0.95);
}

.matching-item.used {
    opacity: 0.5;
    pointer-events: none;
    transform: scale(0.9);
}

.matching-item.correct {
    background: linear-gradient(135deg, #28a745 0%, #34ce57 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.matching-item.incorrect {
    background: linear-gradient(135deg, #dc3545 0%, #e55353 100%);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

/* Animal-specific colors */
.matching-item[data-animal="dog"] {
    background: linear-gradient(135deg, #ff9a56 0%, #ff7676 100%);
    box-shadow: 0 4px 15px rgba(255, 154, 86, 0.3);
}

.matching-item[data-animal="snake"] {
    background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.matching-item[data-animal="elephant"] {
    background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
}

.matching-item[data-animal="alligator"] {
    background: linear-gradient(135deg, #3f51b5 0%, #5c6bc0 100%);
    box-shadow: 0 4px 15px rgba(63, 81, 181, 0.3);
}

.matching-item[data-animal="panda"] {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.matching-item[data-animal="dolphin"] {
    background: linear-gradient(135deg, #ff5722 0%, #ff7043 100%);
    box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
}

.matching-item[data-animal="tiger"] {
    background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);
    box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
}

.matching-item[data-animal="shark"] {
    background: linear-gradient(135deg, #00bcd4 0%, #4dd0e1 100%);
    box-shadow: 0 4px 15px rgba(0, 188, 212, 0.3);
}

.matching-item[data-animal="lion"] {
    background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.matching-item[data-animal="bee"] {
    background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

/* Image Items Column */
.image-items-column {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 2px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.image-drop-zone {
    position: relative;
    width: 180px;
    height: 120px;
    border: 3px dashed #ddd;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background: #f8f9fa;
    overflow: hidden;
    cursor: pointer;
}

.image-drop-zone:hover {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
    transform: scale(1.02);
}

.image-drop-zone.drag-over {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.1);
    transform: scale(1.05);
    border-style: solid;
}

.image-drop-zone.filled {
    border-color: #28a745;
    border-style: solid;
    background: rgba(40, 167, 69, 0.05);
}

.image-drop-zone.correct {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
}

.image-drop-zone.incorrect {
    border-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
}

.image-drop-zone img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

.drop-text {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 10px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 5;
}

.matched-text {
    position: absolute;
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(40, 167, 69, 0.9);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    z-index: 10;
}

/* Control Buttons */
.control-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.reset-btn {
    background: linear-gradient(135deg, #6c757d 0%, #868e96 100%);
    color: white;
}

.reset-btn:hover {
    background: linear-gradient(135deg, #5a6268 0%, #6c757d 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.check-btn {
    background: linear-gradient(135deg, #28a745 0%, #34ce57 100%);
    color: white;
}

.check-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #28a745 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

/* Feedback */
.matching-feedback {
    margin-top: 20px;
    padding: 15px;
    border-radius: 12px;
    text-align: center;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
}

.matching-feedback.correct {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 2px solid #28a745;
    color: #155724;
}

.matching-feedback.incorrect {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 2px solid #dc3545;
    color: #721c24;
}

.matching-feedback.partial {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    color: #856404;
}

/* Responsive Design */
@media (max-width: 768px) {
    .matching-container {
        padding: 15px;
        gap: 20px;
    }
    
    .matching-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .text-items-column,
    .image-items-column {
        width: 100%;
        padding: 15px;
    }
    
    .matching-item {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .image-drop-zone {
        width: 150px;
        height: 100px;
    }
    
    .control-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 200px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .text-items-column {
        gap: 8px;
    }
    
    .matching-item {
        padding: 8px 16px;
        font-size: 13px;
    }
    
    .image-drop-zone {
        width: 130px;
        height: 90px;
    }
    
    .image-items-column {
        gap: 10px;
    }
}
/* CSS cho dạng câu hỏi sắp xếp từ */
.arrangement-container {
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid #e9ecef;
}

.arrangement-instruction {
    margin-bottom: 20px;
    padding: 15px;
    background: #e3f2fd;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
    font-weight: 500;
    color: #0d47a1;
}

.question-image {
    margin-bottom: 20px;
    text-align: center;
}

.question-image img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.words-pool {
    margin-bottom: 25px;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
    min-height: 80px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    justify-content: center;
    position: relative;
}

.words-pool.drag-over {
    border-color: #007bff;
    background: #f0f8ff;
}

.words-pool-label {
    width: 100%;
    text-align: center;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 10px;
}

.word-item {
    padding: 10px 16px;
    background: #007bff;
    color: white;
    border-radius: 25px;
    cursor: grab;
    user-select: none;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.word-item:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.word-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.word-item.correct {
    background: #28a745;
    border-color: #1e7e34;
}

.word-item.incorrect {
    background: #dc3545;
    border-color: #c82333;
}

.word-item:active {
    cursor: grabbing;
}

.word-item.clickable {
    cursor: pointer;
}

.word-item.clickable:hover {
    background: #0056b3;
    transform: translateY(-2px) scale(1.05);
}

.sentence-builder {
    margin-bottom: 20px;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    border: 2px solid #28a745;
    min-height: 80px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    justify-content: flex-start;
}

.sentence-builder.drag-over {
    background: #f0fff0;
    border-color: #20c997;
}

.sentence-builder-label {
    width: 100%;
    text-align: center;
    color: #28a745;
    font-weight: 600;
    margin-bottom: 10px;
}

.sentence-builder.empty .sentence-builder-label::after {
    content: " (Kéo thả hoặc click vào các từ để tạo câu)";
    color: #6c757d;
    font-weight: 400;
}

.arrangement-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.btn-reset-arrangement {
    padding: 10px 20px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.btn-reset-arrangement:hover {
    background: #5a6268;
}

.btn-reset-arrangement:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.arrangement-feedback {
    margin-top: 15px;
    padding: 15px;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
}

.arrangement-feedback.correct {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.arrangement-feedback.incorrect {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.correct-sentence {
    margin-top: 10px;
    padding: 12px;
    background: #e8f5e8;
    border-radius: 6px;
    border-left: 4px solid #28a745;
    font-style: italic;
    color: #155724;
}

/* Animation cho drag and drop */
@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.word-item.just-dropped {
    animation: slideIn 0.3s ease;
}

/* Responsive cho arrangement */
@media (max-width: 768px) {
    .arrangement-container {
        padding: 15px;
    }
    
    .words-pool, .sentence-builder {
        padding: 15px;
        min-height: 60px;
    }
    
    .word-item {
        padding: 8px 12px;
        font-size: 14px;
    }
}
.btn-submit, .btn-complete {
    transition: opacity 0.3s ease;
}

.btn-submit.hidden, .btn-complete.hidden {
    display: none !important;
}