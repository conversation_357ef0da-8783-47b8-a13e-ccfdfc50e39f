<?php $__env->startSection('styles'); ?>
    <style>
        .question-form-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #dee2e6;
        }

        .question-type-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .question-type-card:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
        }

        .question-type-card.active {
            border-color: #007bff;
            background: #e7f3ff;
        }

        .answer-option {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }

        .correct-answer {
            border-color: #28a745;
            background: #d4edda;
        }

        .image-upload-area {
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .image-upload-area:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }

        .matching-pair {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .word-letters {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .letter-box {
            width: 40px;
            height: 40px;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('partials.alerts', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">       
                        <h3 class="card-title">
                            <i class="fas fa-plus-circle me-2"></i>Thêm Câu Hỏi Mới
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Chọn Level và Lesson -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="levelSelect" class="form-label">
                                    <i class="fas fa-layer-group me-1"></i>Trình độ
                                </label>
                                <select id="levelSelect" class="form-control">
                                    <option value="">-- Chọn trình độ --</option>
                                    <?php $__currentLoopData = $levels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($level); ?>"><?php echo e($level); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="lessonSelect" class="form-label">
                                    <i class="fas fa-book me-1"></i>Bài học
                                </label>
                                <select id="lessonSelect" class="form-control">
                                    <option value="">-- Chọn bài học --</option>
                                </select>
                            </div>
                        </div>

                        <!-- Chọn dạng câu hỏi -->
                        <div id="questionTypeSection" style="display: none;">
                            <h5 class="mb-3">
                                <i class="fas fa-question-circle me-2"></i>Chọn Dạng Câu Hỏi
                            </h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="question-type-card" data-type="single_choice">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-list-ul text-primary me-3 fa-2x"></i>
                                            <div>
                                                <h6 class="mb-1">Trắc Nghiệm</h6>
                                                <small class="text-muted">4 đáp án, 1 đáp án đúng</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="question-type-card" data-type="matching">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-link text-success me-3 fa-2x"></i>
                                            <div>
                                                <h6 class="mb-1">Nối Từ</h6>
                                                <small class="text-muted">Nối từ với hình ảnh/nghĩa</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="question-type-card" data-type="classification">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tags text-warning me-3 fa-2x"></i>
                                            <div>
                                                <h6 class="mb-1">Phân Loại Từ</h6>
                                                <small class="text-muted">Danh từ, động từ, tính từ</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="question-type-card" data-type="fill_blank">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-edit text-info me-3 fa-2x"></i>
                                            <div>
                                                <h6 class="mb-1">Điền Chỗ Trống</h6>
                                                <small class="text-muted">Điền từ vào chỗ trống</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="question-type-card" data-type="arrangement">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-sort text-purple me-3 fa-2x"></i>
                                            <div>
                                                <h6 class="mb-1">Sắp Xếp Câu</h6>
                                                <small class="text-muted">Sắp xếp thành câu đúng</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="question-type-card" data-type="image_word">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-image text-danger me-3 fa-2x"></i>
                                            <div>
                                                <h6 class="mb-1">Nhìn Ảnh Ghép Từ</h6>
                                                <small class="text-muted">Xem ảnh sắp xếp thành từ</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form 1: Trắc nghiệm -->
                        <div id="singleChoiceFormContainer" class="question-form-container" style="display: none;">
                            <form id="singleChoiceForm" method="POST" enctype="multipart/form-data"
                                action="<?php echo e(route('admin.questions.store')); ?>">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="lesson_part_id" class="lesson-part-input">
                                <input type="hidden" name="question_type" value="single_choice">

                                <h5 class="mb-3">
                                    <i class="fas fa-list-ul me-2"></i>Câu Hỏi Trắc Nghiệm
                                </h5>
                                <input type="hidden" name="order_index" class="order-index-input">
                                <div class="mb-3">
                                    <label class="form-label">Thứ tự câu hỏi:</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Thứ tự sẽ được tự động tính toán: <span class="order-index-display">Chọn bài học để xem thứ tự</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Câu hỏi:</label>
                                    <textarea class="form-control" name="question_text" rows="3" placeholder="Nhập câu hỏi..."></textarea>
                                </div>
                                <label class="form-label">Đáp án (chọn đáp án đúng):</label>
                                <div id="multipleChoiceAnswers">
                                    <?php $labels = ['A', 'B', 'C', 'D']; ?>
                                    <?php $__currentLoopData = $labels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="answer-option mb-2">
                                            <label class="d-flex align-items-center w-100">
                                                <input type="radio" name="correct_answer" value="<?php echo e($index); ?>"
                                                    class="me-2">
                                                <span class="me-2 fw-bold"><?php echo e($label); ?>.</span>
                                                <input type="text" class="form-control" name="answers[]"
                                                    placeholder="Nhập đáp án <?php echo e($label); ?>">
                                            </label>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Phản hồi khi đúng:</label>
                                    <input type="text" class="form-control" name="correct_feedback"
                                        placeholder="Ví dụ: Chính xác!">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Phản hồi khi sai:</label>
                                    <input type="text" class="form-control" name="wrong_feedback"
                                        placeholder="Ví dụ: Bạn cần ôn lại phần này.">
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-save me-1"></i>Lưu Câu Hỏi
                                    </button>
                                    <button type="button" class="btn btn-secondary cancel-btn">
                                        <i class="fas fa-times me-1"></i>Hủy
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Form 2: Nối từ -->
                        <div id="matchingFormContainer" class="question-form-container" style="display: none;">
                            <form id="matchingForm" method="POST" enctype="multipart/form-data"
                                action="<?php echo e(route('admin.questions.store')); ?>">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="lesson_part_id" class="lesson-part-input">
                                <input type="hidden" name="question_type" value="matching">

                                <h5 class="mb-3">
                                    <i class="fas fa-link me-2"></i>Câu Hỏi Nối Từ
                                </h5>
                                <input type="hidden" name="order_index" class="order-index-input">
                                <div class="mb-3">
                                    <label class="form-label">Thứ tự câu hỏi:</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Thứ tự sẽ được tự động tính toán: <span class="order-index-display">Chọn bài học để xem thứ tự</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Hướng dẫn:</label>
                                    <textarea class="form-control" name="question_text" rows="2"
                                        placeholder="Ví dụ: Nối từ với hình ảnh tương ứng..."></textarea>
                                </div>

                                <label class="form-label">Các từ bên trái (Left Items):</label>
                                <div id="leftItems" class="mb-3">
                                    <div class="input-group mb-2">
                                        <input type="text" class="form-control" name="left_items[]" placeholder="Nhập từ vựng">
                                        <button type="button" class="btn btn-danger remove-left-item">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary mb-3" id="addLeftItem">
                                    <i class="fas fa-plus me-1"></i>Thêm từ
                                </button>

                                <label class="form-label">Hình ảnh bên phải (Right Images):</label>
                                <div id="rightImages" class="mb-3">
                                    <div class="input-group mb-2">
                                        <input type="file" class="form-control" name="right_images[]" accept="image/*">
                                        <button type="button" class="btn btn-danger remove-right-image">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-primary mb-3" id="addRightImage">
                                    <i class="fas fa-plus me-1"></i>Thêm ảnh
                                </button>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Lưu ý:</strong> Số lượng từ và ảnh phải bằng nhau. Thứ tự từ và ảnh phải tương ứng với nhau.
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-save me-1"></i>Lưu Câu Hỏi
                                    </button>
                                    <button type="button" class="btn btn-secondary cancel-btn">
                                        <i class="fas fa-times me-1"></i>Hủy
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Form 3: Phân loại từ -->
                        <div id="classificationFormContainer" class="question-form-container" style="display: none;">
                            <form id="classificationForm" method="POST" enctype="multipart/form-data"
                                action="<?php echo e(route('admin.questions.store')); ?>">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="lesson_part_id" class="lesson-part-input">
                                <input type="hidden" name="question_type" value="classification">

                                <h5 class="mb-3">
                                    <i class="fas fa-tags me-2"></i>Câu Hỏi Phân Loại Từ
                                </h5>
                                <input type="hidden" name="order_index" class="order-index-input">
                                <div class="mb-3">
                                    <label class="form-label">Thứ tự câu hỏi:</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Thứ tự sẽ được tự động tính toán: <span class="order-index-display">Chọn bài học để xem thứ tự</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Câu hỏi:</label>
                                    <textarea class="form-control" name="question_text" rows="2"
                                        placeholder="Ví dụ: Phân loại các từ sau theo loại từ..."></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Danh sách từ và phân loại:</label>
                                    <div id="classificationWords">
                                        <div class="row mb-2">
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" name="words[]" placeholder="Nhập từ">
                                            </div>
                                            <div class="col-md-5">
                                                <select class="form-control" name="word_categories[]">
                                                    <option value="">-- Chọn loại từ --</option>
                                                    <option value="noun">Danh từ (Noun)</option>
                                                    <option value="verb">Động từ (Verb)</option>
                                                    <option value="adjective">Tính từ (Adjective)</option>
                                                    <option value="adverb">Trạng từ (Adverb)</option>
                                                </select>
                                            </div>
                                            <div class="col-md-1">
                                                <button type="button" class="btn btn-danger remove-classification-word">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-primary" id="addClassificationWord">
                                        <i class="fas fa-plus me-1"></i>Thêm từ
                                    </button>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Lưu ý:</strong> Mỗi từ cần được phân loại vào đúng loại từ tương ứng.
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-save me-1"></i>Lưu Câu Hỏi
                                    </button>
                                    <button type="button" class="btn btn-secondary cancel-btn">
                                        <i class="fas fa-times me-1"></i>Hủy
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Form 4: Điền chỗ trống -->
                        <div id="fillBlankFormContainer" class="question-form-container" style="display: none;">
                            <form id="fillBlankForm" method="POST" enctype="multipart/form-data"
                                action="<?php echo e(route('admin.questions.store')); ?>">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="lesson_part_id" class="lesson-part-input">
                                <input type="hidden" name="question_type" value="fill_blank">

                                <h5 class="mb-3">
                                    <i class="fas fa-edit me-2"></i>Câu Hỏi Điền Chỗ Trống
                                </h5>
                                <input type="hidden" name="order_index" class="order-index-input">
                                <div class="mb-3">
                                    <label class="form-label">Thứ tự câu hỏi:</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Thứ tự sẽ được tự động tính toán: <span class="order-index-display">Chọn bài học để xem thứ tự</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Câu hỏi (dùng ___ để đánh dấu chỗ trống):</label>
                                    <textarea class="form-control" name="question_text" rows="3"
                                        placeholder="Ví dụ: I ___ to school every day."></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Các đáp án đúng (có thể có nhiều đáp án):</label>
                                    <div id="correctAnswers">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="correct_answers[]" placeholder="Nhập đáp án đúng">
                                            <button type="button" class="btn btn-danger remove-correct-answer">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-primary mb-3" id="addCorrectAnswer">
                                        <i class="fas fa-plus me-1"></i>Thêm đáp án
                                    </button>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Phản hồi khi đúng:</label>
                                    <input type="text" class="form-control" name="correct_feedback"
                                        placeholder="Ví dụ: Chính xác!">
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Lưu ý:</strong> Có thể có nhiều đáp án đúng cho một chỗ trống (ví dụ: go/walk/run).
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-save me-1"></i>Lưu Câu Hỏi
                                    </button>
                                    <button type="button" class="btn btn-secondary cancel-btn">
                                        <i class="fas fa-times me-1"></i>Hủy
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Form 5: Sắp xếp câu -->
                        <div id="arrangementFormContainer" class="question-form-container" style="display: none;">
                            <form id="arrangementForm" method="POST" enctype="multipart/form-data"
                                action="<?php echo e(route('admin.questions.store')); ?>">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="lesson_part_id" class="lesson-part-input">
                                <input type="hidden" name="question_type" value="arrangement">

                                <h5 class="mb-3">
                                    <i class="fas fa-sort me-2"></i>Câu Hỏi Sắp Xếp Câu
                                </h5>
                                <input type="hidden" name="order_index" class="order-index-input">
                                <div class="mb-3">
                                    <label class="form-label">Thứ tự câu hỏi:</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Thứ tự sẽ được tự động tính toán: <span class="order-index-display">Chọn bài học để xem thứ tự</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Hướng dẫn:</label>
                                    <textarea class="form-control" name="question_text" rows="2"
                                        placeholder="Ví dụ: Sắp xếp các từ sau thành câu đúng..."></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Các từ theo thứ tự đúng:</label>
                                    <div id="sentenceWords">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="sentence_words[]" placeholder="Nhập từ">
                                            <button type="button" class="btn btn-danger remove-sentence-word">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-primary mb-3" id="addSentenceWord">
                                        <i class="fas fa-plus me-1"></i>Thêm từ
                                    </button>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Preview câu đúng:</label>
                                    <div id="sentencePreview" class="alert alert-light"></div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Lưu ý:</strong> Nhập các từ theo thứ tự đúng. Hệ thống sẽ tự động xáo trộn khi hiển thị cho học viên.
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-save me-1"></i>Lưu Câu Hỏi
                                    </button>
                                    <button type="button" class="btn btn-secondary cancel-btn">
                                        <i class="fas fa-times me-1"></i>Hủy
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Form 6: Nhìn ảnh ghép từ -->
                        <div id="imageWordFormContainer" class="question-form-container" style="display: none;">
                            <form id="imageWordForm" method="POST" enctype="multipart/form-data"
                                action="<?php echo e(route('admin.questions.store')); ?>">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="lesson_part_id" class="lesson-part-input">
                                <input type="hidden" name="question_type" value="image_word">

                                <h5 class="mb-3">
                                    <i class="fas fa-image me-2"></i>Câu Hỏi Nhìn Ảnh Ghép Từ
                                </h5>
                                <input type="hidden" name="order_index" class="order-index-input">
                                <div class="mb-3">
                                    <label class="form-label">Thứ tự câu hỏi:</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Thứ tự sẽ được tự động tính toán: <span class="order-index-display">Chọn bài học để xem thứ tự</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Câu hỏi:</label>
                                    <textarea class="form-control" name="question_text" rows="2"
                                        placeholder="Ví dụ: Nhìn hình ảnh và sắp xếp các chữ cái thành từ đúng..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Hình ảnh:</label>
                                    <div class="image-upload-area" data-form="imageWord">
                                        <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                        <p class="text-muted">Click để chọn ảnh</p>
                                        <input type="file" class="form-control" name="image" accept="image/*"
                                            style="display: none;">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Từ đúng:</label>
                                    <input type="text" class="form-control" name="correct_word"
                                        placeholder="Nhập từ đúng (chỉ chữ cái)" id="correctWordInput" pattern="[a-zA-Z]+">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Preview các chữ cái:</label>
                                    <div id="letterBoxes" class="word-letters"></div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Lưu ý:</strong> Từ đúng chỉ được chứa chữ cái (a-z, A-Z). Hệ thống sẽ tự động tách thành các chữ cái riêng biệt.
                                </div>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-save me-1"></i>Lưu Câu Hỏi
                                    </button>
                                    <button type="button" class="btn btn-secondary cancel-btn">
                                        <i class="fas fa-times me-1"></i>Hủy
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        let selectedQuestionType = '';
        let selectedLessonPartId = '';

        $(document).ready(function() {
            // Xử lý chọn level
            $('#levelSelect').on('change', function() {
                const level = $(this).val();
                $('#lessonSelect').html('<option value="">-- Đang tải bài học... --</option>');
                $('#questionTypeSection').hide();
                hideAllForms();

                if (level) {
                    $.ajax({
                        url: `/admin/api/lessons/by-level/${level}`,
                        method: 'GET',
                        success: function(data) {
                            let options = '<option value="">-- Chọn bài học --</option>';
                            data.forEach(part => {
                                options +=
                                    `<option value="${part.lesson_part_id}">${part.content}</option>`;
                            });
                            $('#lessonSelect').html(options);
                        },
                        error: function() {
                            alert('Không thể tải bài học');
                        }
                    });
                }
            });

            // Xử lý chọn lesson
            $('#lessonSelect').on('change', function() {
                const lessonPartId = $(this).val();
                if (lessonPartId) {
                    selectedLessonPartId = lessonPartId;
                    $('#questionTypeSection').show();
                    $('.lesson-part-input').val(lessonPartId);

                    // Lấy order_index tiếp theo
                    fetchNextOrderIndex(lessonPartId);
                } else {
                    $('#questionTypeSection').hide();
                    hideAllForms();
                    // Reset order index display
                    $('.order-index-display').text('Chọn bài học để xem thứ tự');
                    $('.order-index-input').val('');
                }
            });

            // Xử lý chọn dạng câu hỏi
            $('.question-type-card').on('click', function() {
                $('.question-type-card').removeClass('active');
                $(this).addClass('active');

                selectedQuestionType = $(this).data('type');
                showQuestionForm(selectedQuestionType);
            });

            // Xử lý upload ảnh
            $('.image-upload-area').on('click', function() {
                $(this).find('input[type="file"]').click();
            });

            // Xử lý thêm left item
            $('#addLeftItem').on('click', function() {
                const newItem = `
                    <div class="input-group mb-2">
                        <input type="text" class="form-control" name="left_items[]" placeholder="Nhập từ vựng">
                        <button type="button" class="btn btn-danger remove-left-item">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                $('#leftItems').append(newItem);
            });

            // Xử lý xóa left item
            $(document).on('click', '.remove-left-item', function() {
                if ($('#leftItems .input-group').length > 1) {
                    $(this).closest('.input-group').remove();
                }
            });

            // Xử lý thêm right image
            $('#addRightImage').on('click', function() {
                const newImage = `
                    <div class="input-group mb-2">
                        <input type="file" class="form-control" name="right_images[]" accept="image/*">
                        <button type="button" class="btn btn-danger remove-right-image">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                $('#rightImages').append(newImage);
            });

            // Xử lý xóa right image
            $(document).on('click', '.remove-right-image', function() {
                if ($('#rightImages .input-group').length > 1) {
                    $(this).closest('.input-group').remove();
                }
            });

            // Xử lý thêm classification word
            $('#addClassificationWord').on('click', function() {
                const newWord = `
                    <div class="row mb-2">
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="words[]" placeholder="Nhập từ">
                        </div>
                        <div class="col-md-5">
                            <select class="form-control" name="word_categories[]">
                                <option value="">-- Chọn loại từ --</option>
                                <option value="noun">Danh từ (Noun)</option>
                                <option value="verb">Động từ (Verb)</option>
                                <option value="adjective">Tính từ (Adjective)</option>
                                <option value="adverb">Trạng từ (Adverb)</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-danger remove-classification-word">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
                $('#classificationWords').append(newWord);
            });

            // Xử lý xóa classification word
            $(document).on('click', '.remove-classification-word', function() {
                if ($('#classificationWords .row').length > 1) {
                    $(this).closest('.row').remove();
                }
            });

            // Xử lý thêm correct answer
            $('#addCorrectAnswer').on('click', function() {
                const newAnswer = `
                    <div class="input-group mb-2">
                        <input type="text" class="form-control" name="correct_answers[]" placeholder="Nhập đáp án đúng">
                        <button type="button" class="btn btn-danger remove-correct-answer">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                $('#correctAnswers').append(newAnswer);
            });

            // Xử lý xóa correct answer
            $(document).on('click', '.remove-correct-answer', function() {
                if ($('#correctAnswers .input-group').length > 1) {
                    $(this).closest('.input-group').remove();
                }
            });

            // Xử lý thêm sentence word
            $('#addSentenceWord').on('click', function() {
                const newWord = `
                    <div class="input-group mb-2">
                        <input type="text" class="form-control" name="sentence_words[]" placeholder="Nhập từ">
                        <button type="button" class="btn btn-danger remove-sentence-word">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                $('#sentenceWords').append(newWord);
            });

            // Xử lý xóa sentence word
            $(document).on('click', '.remove-sentence-word', function() {
                if ($('#sentenceWords .input-group').length > 1) {
                    $(this).closest('.input-group').remove();
                }
            });

            // Cập nhật preview câu khi thay đổi sentence words
            $(document).on('input', 'input[name="sentence_words[]"]', function() {
                updateSentencePreview();
            });

            // Function để cập nhật sentence preview
            function updateSentencePreview() {
                const words = $('input[name="sentence_words[]"]').map(function() {
                    return $(this).val().trim();
                }).get().filter(word => word.length > 0);

                if (words.length > 0) {
                    $('#sentencePreview').html(words.join(' '));
                } else {
                    $('#sentencePreview').html('Nhập các từ để xem preview...');
                }
            }

        // Function để lấy order_index tiếp theo
        function fetchNextOrderIndex(lessonPartId) {
            $.ajax({
                url: `/admin/api/questions/next-order-index/${lessonPartId}`,
                method: 'GET',
                success: function(response) {
                    const nextOrder = response.next_order_index;
                    $('.order-index-input').val(nextOrder);
                    $('.order-index-display').text(`Câu hỏi thứ ${nextOrder}`);
                },
                error: function() {
                    $('.order-index-display').text('Không thể lấy thứ tự câu hỏi');
                    $('.order-index-input').val(1); // Default value
                }
            });
        }

            // Tự động tạo ô chữ cái cho ghép từ
            $(document).on('input', '#correctWordInput', function() {
                const word = $(this).val().toUpperCase();
                if (word) {
                    const lettersHtml = word.split('').map(letter =>
                        `<div class="letter-box">${letter}</div>`
                    ).join('');
                    $('#letterBoxes').html(lettersHtml);
                }
            });

            // Xử lý nút hủy
            $('.cancel-btn').on('click', function() {
                hideAllForms();
                $('.question-type-card').removeClass('active');
                selectedQuestionType = '';
            });

            // Xử lý radio button cho trắc nghiệm
            $(document).on('change', 'input[name="correct_answer"]', function() {
                $('.answer-option').removeClass('correct-answer');
                $(this).closest('.answer-option').addClass('correct-answer');
            });

            // Xử lý preview ảnh
            $(document).on('change', 'input[type="file"]', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    const $container = $(this).closest('.image-upload-area, .matching-pair');

                    reader.onload = function(e) {
                        const preview = `
                            <div class="preview mt-2">
                                <img src="${e.target.result}" alt="Preview" 
                                     style="max-width: 200px; max-height: 200px; border-radius: 5px;">
                            </div>
                        `;
                        $container.find('.preview').remove();
                        $container.append(preview);
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Validation trước khi submit
            $(document).on('submit', 'form', function(e) {
                const formType = $(this).attr('id');
                let isValid = true;

                switch (formType) {
                    case 'singleChoiceForm':
                        isValid = validateSingleChoice();
                        break;
                    case 'matchingForm':
                        isValid = validateMatching();
                        break;
                    case 'classificationForm':
                        isValid = validateClassification();
                        break;
                    case 'fillBlankForm':
                        isValid = validateFillBlank();
                        break;
                    case 'arrangementForm':
                        isValid = validateArrangement();
                        break;
                    case 'imageWordForm':
                        isValid = validateImageWord();
                        break;
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });
        });

        function showQuestionForm(type) {
            hideAllForms();

            const formMap = {
                'single_choice': '#singleChoiceFormContainer',
                'matching': '#matchingFormContainer',
                'classification': '#classificationFormContainer',
                'fill_blank': '#fillBlankFormContainer',
                'arrangement': '#arrangementFormContainer',
                'image_word': '#imageWordFormContainer'
            };

            const targetForm = formMap[type];
            if (targetForm) {
                $(targetForm).show();
                console.log('Showing form:', targetForm); // Debug log
            } else {
                console.error('Form type not found:', type);
            }
        }

        function hideAllForms() {
            $('.question-form-container').hide();
        }

        // Validation functions
        function validateSingleChoice() {
            const question = $('textarea[name="question_text"]').val();
            const answers = $('input[name="answers[]"]').map(function() {
                return $(this).val().trim();
            }).get();
            const correctAnswer = $('input[name="correct_answer"]:checked').val();

            if (!question.trim()) {
                alert('Vui lòng nhập câu hỏi');
                return false;
            }

            // Kiểm tra tất cả đáp án đã được nhập
            if (answers.some(answer => !answer)) {
                alert('Vui lòng nhập đầy đủ 4 đáp án');
                return false;
            }

            // Kiểm tra đáp án có trùng lặp không
            const uniqueAnswers = [...new Set(answers)];
            if (uniqueAnswers.length !== answers.length) {
                alert('Các đáp án không được trùng lặp. Vui lòng nhập 4 đáp án khác nhau.');
                return false;
            }


            if (correctAnswer === undefined) {
                alert('Vui lòng chọn đáp án đúng cho câu hỏi');
                return false;
            }

            return true;
        }

        function validateMatching() {
            const leftItems = $('#matchingForm input[name="left_items[]"]').map(function() {
                return $(this).val().trim();
            }).get();
            const rightImages = $('#matchingForm input[name="right_images[]"]').map(function() {
                return this.files[0];
            }).get();

            if (leftItems.length < 2) {
                alert('Cần ít nhất 2 từ bên trái');
                return false;
            }

            if (rightImages.length < 2) {
                alert('Cần ít nhất 2 ảnh bên phải');
                return false;
            }

            if (leftItems.length !== rightImages.length) {
                alert('Số lượng từ và ảnh phải bằng nhau');
                return false;
            }

            if (leftItems.some(item => !item)) {
                alert('Vui lòng nhập đầy đủ từ vựng');
                return false;
            }

            if (rightImages.some(image => !image)) {
                alert('Vui lòng chọn đầy đủ hình ảnh');
                return false;
            }

            return true;
        }

        function validateClassification() {
            const words = $('#classificationForm input[name="words[]"]').map(function() {
                return $(this).val().trim();
            }).get();
            const categories = $('#classificationForm select[name="word_categories[]"]').map(function() {
                return $(this).val();
            }).get();

            if (words.length < 3) {
                alert('Cần ít nhất 3 từ để phân loại');
                return false;
            }

            if (words.some(word => !word)) {
                alert('Vui lòng nhập đầy đủ tất cả các từ');
                return false;
            }

            if (categories.some(category => !category)) {
                alert('Vui lòng chọn loại từ cho tất cả các từ');
                return false;
            }

            return true;
        }

        function validateFillBlank() {
            const question = $('#fillBlankForm textarea[name="question_text"]').val();
            const correctAnswers = $('#fillBlankForm input[name="correct_answers[]"]').map(function() {
                return $(this).val().trim();
            }).get();

            if (!question.trim()) {
                alert('Vui lòng nhập câu hỏi');
                return false;
            }

            if (!question.includes('___')) {
                alert('Câu hỏi phải có ít nhất một chỗ trống (___)');
                return false;
            }

            if (correctAnswers.length === 0 || correctAnswers.some(answer => !answer)) {
                alert('Vui lòng nhập ít nhất một đáp án đúng');
                return false;
            }

            return true;
        }

        function validateArrangement() {
            const words = $('#arrangementForm input[name="sentence_words[]"]').map(function() {
                return $(this).val().trim();
            }).get().filter(word => word.length > 0);

            if (words.length < 2) {
                alert('Câu phải có ít nhất 2 từ');
                return false;
            }

            if ($('#arrangementForm input[name="sentence_words[]"]').map(function() {
                return $(this).val().trim();
            }).get().some(word => !word)) {
                alert('Vui lòng nhập đầy đủ tất cả các từ');
                return false;
            }

            return true;
        }

        function validateImageWord() {
            const image = $('#imageWordForm input[name="image"]')[0].files[0];
            const correctWord = $('#imageWordForm input[name="correct_word"]').val().trim();

            if (!image) {
                alert('Vui lòng chọn hình ảnh');
                return false;
            }

            if (!correctWord) {
                alert('Vui lòng nhập từ đúng');
                return false;
            }

            if (correctWord.length < 2) {
                alert('Từ phải có ít nhất 2 ký tự');
                return false;
            }

            if (!/^[a-zA-Z]+$/.test(correctWord)) {
                alert('Từ chỉ được chứa chữ cái (a-z, A-Z)');
                return false;
            }

            return true;
        }

        // Utility functions
        function resetAllForms() {
            $('form').each(function() {
                this.reset();
            });
            hideAllForms();
            $('.question-type-card').removeClass('active');
            selectedQuestionType = '';
            $('.preview').remove();
            $('#scrambledWords').html('');
            $('#letterBoxes').html('');
        }

        // Debug function
        function debugFormVisibility() {
            $('.question-form-container').each(function() {
                console.log($(this).attr('id'), $(this).is(':visible'));
            });
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Temp\nhut\resources\views/admin/AddQuestion.blade.php ENDPATH**/ ?>